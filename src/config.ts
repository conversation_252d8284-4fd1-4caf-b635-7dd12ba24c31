interface EnvironmentConfig {
  host: string;
}

const baseApi: { [key: string]: EnvironmentConfig } = {
  // 开发版
  development: {
    host: "http://************:7861",
  },
  // 体验版
  trial: {
    host: "http://************:7861",
  },
  // 正式版
  release: {
    host: "http://************:7861",
  },
  // uniapp 正式环境
  production: {
    host: "http://************:7861",
  },
};
// 环境
export const env = process.env.NODE_ENV;
// host
export const baseUrl = baseApi[env].host;
