{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "sourceMap": true, "skipLibCheck": true, "baseUrl": ".", "types": ["webpack-env", "@dcloudio/types", "miniprogram-api-typings"], "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "exclude": ["node_modules", "unpackage", "dist", "src/**/*.nvue"]}